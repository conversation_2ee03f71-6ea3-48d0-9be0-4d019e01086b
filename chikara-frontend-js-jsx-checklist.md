# Chikara Frontend JS/JSX Files Checklist

This document contains a checklist of all remaining `.js` and `.jsx` files in the `chikara-frontend/src` directory that may need to be migrated to TypeScript.

## Components

### Core Components

- [ ] `components/Polls/SurveyComponent.jsx`
- [ ] `components/Polls/SurveyResults.jsx`
- [ ] `components/Profile/ProfileComments.jsx`
- [ ] `components/Profile/SingleComment.jsx`
- [ ] `components/RooftopBattleList.jsx`
- [ ] `components/ServiceWorker/ServiceWorkerUpdater.jsx`
- [ ] `components/SpeechBubble.jsx`
- [ ] `components/Spinners/ButtonSpinner.jsx`
- [ ] `components/TestComponents/WarningIcon.jsx`
- [ ] `components/ToastHandler.jsx`
- [ ] `components/ToastManager.jsx`
- [ ] `components/ToggleSwitch.jsx`
- [ ] `components/Tooltips.jsx`
- [ ] `components/TraderRep.jsx`
- [ ] `components/UsersTable.jsx`

## Features

### Arcade

- [ ] `features/arcade/Arcade.jsx`

### Auction

- [ ] `features/auction/components/ListAuctionModal.jsx`
- [ ] `features/auction/components/MarketTable.jsx`

### Auth

- [ ] `features/auth/components/RegistrationDisabled.jsx`
- [ ] `features/auth/views/AuthRedirect.jsx`
- [ ] `features/auth/views/ForgotPassword.jsx`
- [ ] `features/auth/views/Login.jsx`
- [ ] `features/auth/views/PasswordReset.jsx`
- [ ] `features/auth/views/RegisterCodeInput.jsx`

### Bank

- [ ] `features/bank/components/TransactionHistory.jsx`

### Battle

- [ ] `features/battle/components/BattleAnimationController.jsx`

### Casino

- [ ] `features/casino/components/Lottery.jsx`
- [ ] `features/casino/components/WheelSpin.jsx`

### Character

- [ ] `features/character/api/useEquipItem.jsx`
- [ ] `features/character/components/EquipTooltip.jsx`
- [ ] `features/character/components/InventoryTable.jsx`
- [ ] `features/character/components/SpecialItemModal.jsx`

### Chat

- [ ] `features/chat/components/ChatDropdownButton.jsx`
- [ ] `features/chat/components/ChatTextAreaInput.jsx`
- [ ] `features/chat/components/ChatTopPanel.jsx`
- [ ] `features/chat/components/ConsoleMessage.jsx`
- [ ] `features/chat/components/EmotePicker.jsx`
- [ ] `features/chat/components/RenderChatText.jsx`

### Classroom

- [ ] `features/classroom/components/ClassChatroom.jsx`
- [ ] `features/classroom/components/ClassShop.jsx`
- [ ] `features/classroom/components/ClassTabs.jsx`
- [ ] `features/classroom/components/Exam.jsx`
- [ ] `features/classroom/components/ExamRanking.jsx`
- [ ] `features/classroom/components/ExamsTab.jsx`

### Daily Task

- [ ] `features/dailytask/components/DailyRewardDisplay.jsx`

### Faculty List

- [ ] `features/facultylist/ClassFilter.jsx`
- [ ] `features/facultylist/RoleFilter.jsx`

### Gang

- [ ] `features/gang/components/CreateGangModal.jsx`
- [ ] `features/gang/components/GangBanner.jsx`
- [ ] `features/gang/components/GangChatroom.jsx`
- [ ] `features/gang/components/GangHideout.jsx`
- [ ] `features/gang/components/GangInfo.jsx`
- [ ] `features/gang/components/GangInviteModal.jsx`
- [ ] `features/gang/components/GangLogDisplay.jsx`
- [ ] `features/gang/components/GangMemberModal.jsx`
- [ ] `features/gang/components/GangPayoutsInfo.jsx`
- [ ] `features/gang/components/GangSettingsModal.jsx`
- [ ] `features/gang/components/GangShop.jsx`
- [ ] `features/gang/components/ViewGangModal.jsx`
- [ ] `features/gang/components/YourGang.jsx`

### Home

- [ ] `features/home/<USER>/HomeCalendar.jsx`
- [ ] `features/home/<USER>/HomeNavButton.jsx`

### Hospital

- [ ] `features/hospital/components/HospitalInjuryPanel.jsx`
- [ ] `features/hospital/components/HospitalisationReason.jsx`

### News

- [ ] `features/news/components/ChangelogCard.jsx`
- [ ] `features/news/components/PostContent.jsx`
- [ ] `features/news/components/PublicNewsWrapper.jsx`

### Notifications

- [ ] `features/notifications/components/AdminActionEvent.jsx`
- [ ] `features/notifications/components/AttackedWin.jsx`
- [ ] `features/notifications/components/AuctionEvent.jsx`
- [ ] `features/notifications/components/BankTransferEvent.jsx`
- [ ] `features/notifications/components/BountyEvent.jsx`
- [ ] `features/notifications/components/CommentEvent.jsx`
- [ ] `features/notifications/components/DeathNoteEvent.jsx`
- [ ] `features/notifications/components/EventManager.jsx`
- [ ] `features/notifications/components/FightWinEvent.jsx`
- [ ] `features/notifications/components/GangEvent.jsx`
- [ ] `features/notifications/components/HospitalisedEvent.jsx`
- [ ] `features/notifications/components/JailEvent.jsx`
- [ ] `features/notifications/components/JobEvent.jsx`
- [ ] `features/notifications/components/LevelupEvent.jsx`
- [ ] `features/notifications/components/LifeNoteEvent.jsx`
- [ ] `features/notifications/components/MissionEvent.jsx`
- [ ] `features/notifications/components/NewPatchNotes.jsx`
- [ ] `features/notifications/components/ZombieEvent.jsx`

### Private Message

- [ ] `features/privatemessage/components/MessagePreview.jsx`
- [ ] `features/privatemessage/components/MessageWindow.jsx`
- [ ] `features/privatemessage/components/PrivateMessage.jsx`
- [ ] `features/privatemessage/components/SendMessageForm.jsx`

### Settings

- [ ] `features/settings/components/EmailSettings.jsx`
- [ ] `features/settings/components/ImageCropper.jsx`
- [ ] `features/settings/components/InterfaceSettings.jsx`
- [ ] `features/settings/components/PasswordSettings.jsx`
- [ ] `features/settings/components/ProfileSettings.jsx`

### Shop

- [ ] `features/shop/components/PurchaseItemModal.jsx`
- [ ] `features/shop/components/SellItemModal.jsx`
- [ ] `features/shop/components/ShopBuyItems.jsx`
- [ ] `features/shop/components/ShopItem.jsx`
- [ ] `features/shop/components/ShopItemSell.jsx`
- [ ] `features/shop/components/ShopSellItems.jsx`
- [ ] `features/shop/components/SingleShopkeeper.jsx`

### Streets

- [ ] `features/streets/components/EncounterView.jsx`
- [ ] `features/streets/components/LocationSelect.jsx`
- [ ] `features/streets/components/MapView.jsx`
- [ ] `features/streets/components/ReactFlow.jsx`
- [ ] `features/streets/components/ReactFlowNode.jsx`
- [ ] `features/streets/components/StartStreets.jsx`
- [ ] `features/streets/components/Streets.jsx`

### Suggestions

- [ ] `features/suggestions/components/SuggestionComments.jsx`

### Talents

- [ ] `features/talents/components/DisplayTalent.jsx`
- [ ] `features/talents/components/EmptyTalentSpace.jsx`
- [ ] `features/talents/components/LockedTalent.jsx`
- [ ] `features/talents/components/MainTalent.jsx`
- [ ] `features/talents/components/RespecTalents.jsx`
- [ ] `features/talents/components/TalentInfoModal.jsx`

## Pages

- [ ] `pages/AdventurePage.jsx`
- [ ] `pages/Calendar.jsx`
- [ ] `pages/Casino.jsx`
- [ ] `pages/Classroom.jsx`
- [ ] `pages/CombinedExplore.jsx`
- [ ] `pages/ConstructionPage.jsx`
- [ ] `pages/Courses.jsx`
- [ ] `pages/DailyTaskPage.jsx`
- [ ] `pages/Events.jsx`
- [ ] `pages/Explore.jsx`
- [ ] `pages/FullChat.jsx`
- [ ] `pages/GameStats.jsx`
- [ ] `pages/Gang.jsx`
- [ ] `pages/GangLeaderboards.jsx`
- [ ] `pages/GangList.jsx`
- [ ] `pages/Hospital.jsx`
- [ ] `pages/Inbox.jsx`
- [ ] `pages/Jail.jsx`
- [ ] `pages/LatestNews.jsx`
- [ ] `pages/Market.jsx`
- [ ] `pages/NotFoundPage.jsx`
- [ ] `pages/Polls.jsx`
- [ ] `pages/Profile.jsx`
- [ ] `pages/Referrals.jsx`
- [ ] `pages/Rooftop.jsx`
- [ ] `pages/Settings.jsx`
- [ ] `pages/ShoeLocker.jsx`

## Summary

**Total Files Found:** 168 files

- **Components:** 32 files
- **Features:** 108 files
- **Pages:** 28 files

**Note:** No `.js` files were found in the `chikara-frontend/src` directory. All files listed above are `.jsx` files.

## Migration Notes

When migrating these files to TypeScript:

1. Rename file extensions from `.jsx` to `.tsx`
2. Add proper TypeScript types for props, state, and function parameters
3. Import React explicitly if not already done
4. Add proper type annotations for event handlers
5. Consider using React.FC or proper function component typing
6. Add proper types for any external library usage
7. Update any import statements that may need type adjustments
