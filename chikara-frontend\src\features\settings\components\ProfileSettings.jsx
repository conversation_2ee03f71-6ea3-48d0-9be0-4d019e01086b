import uploadImageIcon from "@/assets/icons/uploadimageicon.svg";
import defaultAvatar from "@/assets/images/defaultAvatar.png";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import useUpdateProfile from "../api/useUpdateProfile";
import ImageCropper from "./ImageCropper";

export default function ProfileSettings({ currentUser }) {
    const [selectedAvatar, setSelectedAvatar] = useState(null);
    const [avatarPreview, setAvatarPreview] = useState();
    const [selectedBanner, setSelectedBanner] = useState(null);
    const [selectedCrop, setSelectedCrop] = useState(null);
    const [bannerPreview, setBannerPreview] = useState(null);
    const [openCropper, setOpenCropper] = useState(false);
    const [username, setUsername] = useState(currentUser?.username);
    const [description, setDescription] = useState(currentUser?.about || "");
    const [gender, setGender] = useState("male");
    const updateProfileMutation = useUpdateProfile();

    // TODO - Implement this
    const isProfileBannerQuestComplete = true;

    const handleSubmit = (e) => {
        e.preventDefault();

        if (username.length > 17) {
            toast.error("Student name is too long!");
            return;
        }
        if (username.length < 3) {
            toast.error("Student name is too short!");
            return;
        }
        if (description.length > 500) {
            toast.error("Description is too long!");
            return;
        }

        // Create the input object for ORPC
        const updateData = {
            username,
            about: description,
        };

        if (selectedAvatar) {
            if (currentUser?.userType === "admin" && import.meta.env.MODE !== "development") {
                toast.error("Don't change admin avatars");
                return;
            }
            updateData.avatar = selectedAvatar;
        }

        if (selectedBanner) {
            updateData.banner = selectedBanner.blob;
        }

        updateProfileMutation.mutate(updateData);
    };

    useEffect(() => {
        if (!selectedAvatar) {
            if (currentUser?.avatar) {
                setAvatarPreview(`/${currentUser?.avatar}`);
            } else {
                setAvatarPreview(defaultAvatar);
            }

            return;
        }
        const objectUrl = URL.createObjectURL(selectedAvatar);
        setAvatarPreview(objectUrl);

        // free memory when ever this component is unmounted
        return () => URL.revokeObjectURL(objectUrl);
    }, [selectedAvatar]);

    const uploadBanner = (e) => {
        e.preventDefault();
        setSelectedCrop(e.target.files[0]);
        setOpenCropper(true);
    };

    useEffect(() => {
        if (!selectedBanner) {
            if (currentUser?.profileBanner) {
                setBannerPreview(`/${currentUser?.profileBanner}`);
            } else {
                setBannerPreview(uploadImageIcon);
            }

            return;
        }
        // const objectUrl = URL.createObjectURL(selectedBanner);
        // setBannerPreview(objectUrl);

        // // free memory when ever this component is unmounted
        // return () => URL.revokeObjectURL(objectUrl);
    }, [selectedBanner]);

    return (
        <form className="divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600" onSubmit={handleSubmit}>
            <div className="px-4 py-2 sm:p-6 md:py-6 lg:pb-8">
                <div>
                    <h2 className="font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200">
                        Profile
                    </h2>
                    <p className="mt-1 text-gray-500 text-sm dark:text-amber-600">
                        This information will be displayed publicly so be careful what you share.
                    </p>
                </div>

                <div className="mt-6 flex flex-col lg:flex-row">
                    <div className="grow space-y-6">
                        <div>
                            <label
                                htmlFor="username"
                                className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                            >
                                Student Name
                            </label>
                            <div className="mt-1 flex rounded-md shadow-xs">
                                <input
                                    value={username}
                                    type="text"
                                    name="username"
                                    id="username"
                                    autoComplete="username"
                                    className="block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                    onChange={(e) => {
                                        setUsername(e.target.value);
                                    }}
                                />
                            </div>
                        </div>

                        <div>
                            <label
                                htmlFor="about"
                                className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                            >
                                Profile Description
                            </label>
                            <div className="mt-1">
                                <textarea
                                    id="about"
                                    name="about"
                                    rows={3}
                                    maxLength={500}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                    value={description}
                                    onChange={(e) => {
                                        setDescription(e.target.value);
                                    }}
                                />
                            </div>
                            <p className="mt-2 text-gray-500 text-sm dark:font-normal dark:text-gray-400">
                                Brief description to be shown on your profile.
                            </p>
                        </div>
                    </div>

                    <div className="mt-6 grow lg:mt-0 lg:ml-6 lg:shrink-0 lg:grow-0">
                        <p
                            className="mb-3 block font-bold text-gray-700 text-xs uppercase tracking-wide md:text-center dark:font-normal dark:text-gray-300"
                            aria-hidden="true"
                        >
                            Avatar
                        </p>
                        <div className="mt-1 lg:hidden">
                            <div className="flex items-center">
                                <div
                                    className="inline-block size-12 shrink-0 overflow-hidden rounded-full"
                                    aria-hidden="true"
                                >
                                    <img className="size-full rounded-full" src={avatarPreview} alt="" />
                                </div>
                                <div className="ml-5 rounded-md shadow-xs">
                                    <div className="group relative flex items-center justify-center rounded-md border border-gray-300 px-3 py-2 focus-within:ring-2 focus-within:ring-light-blue-500 focus-within:ring-offset-2 hover:bg-gray-50 dark:border-blue-600 dark:bg-blue-700 dark:text-stroke-sm">
                                        <label
                                            htmlFor="user-photo-mobile"
                                            className="pointer-events-none relative font-medium text-gray-700 text-sm leading-4 dark:font-normal dark:text-white"
                                        >
                                            {/* MOBILE */}
                                            <span>Change Avatar</span>
                                            <span className="sr-only"> user photo</span>
                                        </label>
                                        <input
                                            id="user-photo-mobile"
                                            name="user-photo-mobile"
                                            type="file"
                                            accept="image/*"
                                            className="absolute size-full cursor-pointer rounded-md border-gray-300 opacity-0"
                                            onChange={(e) => setSelectedAvatar(e.target.files[0])}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="relative hidden overflow-hidden rounded-full lg:block">
                            <img className="relative size-40 rounded-full" src={avatarPreview} alt="" />
                            <label
                                htmlFor="user-photo"
                                className="absolute inset-0 flex size-full items-center justify-center bg-black/75 font-medium text-sm text-white opacity-0 focus-within:opacity-100 hover:opacity-100"
                            >
                                {/* DESKTOP */}
                                <span>Change</span>
                                <span className="sr-only"> user photo</span>
                                <input
                                    type="file"
                                    id="user-photo"
                                    name="user-photo"
                                    accept="image/*"
                                    className="absolute inset-0 size-full cursor-pointer rounded-md border-gray-300 opacity-0"
                                    onChange={(e) => setSelectedAvatar(e.target.files[0])}
                                />
                            </label>
                        </div>
                    </div>
                </div>

                <div className="mt-6 grid grid-cols-12 gap-6">
                    <div className="col-span-12 sm:col-span-2">
                        <label
                            htmlFor="first_name"
                            className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                        >
                            Gender
                        </label>
                        <select
                            id="location"
                            name="location"
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pr-10 pl-3 text-base focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                            value={gender}
                            onChange={(e) => setGender(e.target.value)}
                        >
                            <option value="male">--</option>
                            {/* <option value="female">Female</option> */}
                        </select>
                    </div>

                    <div className="col-span-12">
                        <label className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300">
                            Profile Banner Image {!isProfileBannerQuestComplete && "(Requires Task Completion)"}
                        </label>
                        <div className="flex w-full items-center justify-center">
                            <label
                                htmlFor="profile-banner"
                                className={cn(
                                    selectedBanner || currentUser?.profileBanner
                                        ? "h-fit max-h-52 overflow-hidden border-4 border-dashed lg:h-fit lg:max-h-full dark:border-gray-900"
                                        : "mb-2 flex h-32 w-full flex-col border-4 border-dashed md:mb-0 dark:border-gray-500",
                                    "cursor-pointer"
                                )}
                            >
                                <div className="flex flex-col items-center justify-center">
                                    <img
                                        src={bannerPreview}
                                        alt=""
                                        className={cn(
                                            selectedBanner || currentUser?.profileBanner
                                                ? "w-full object-cover object-center"
                                                : "mt-5 size-16 text-gray-400 group-hover:text-gray-600",
                                            ""
                                        )}
                                    />
                                    {!selectedBanner && !currentUser?.profileBanner ? (
                                        <p className="text-gray-400 text-sm tracking-wider group-hover:text-gray-600">
                                            --
                                        </p>
                                    ) : null}
                                </div>

                                <input
                                    disabled={!isProfileBannerQuestComplete}
                                    type="file"
                                    accept="image/*"
                                    id="profile-banner"
                                    name="profile-banner"
                                    className="hidden"
                                    onChange={(e) => uploadBanner(e)}
                                />
                            </label>
                        </div>
                        {selectedBanner?.name && (
                            <p className="pt-1 text-center text-gray-400 text-sm tracking-wider group-hover:text-gray-600">
                                {selectedBanner.name}
                            </p>
                        )}

                        <ImageCropper
                            src={selectedCrop}
                            setPreview={setBannerPreview}
                            setSelected={setSelectedBanner}
                            open={openCropper}
                            setOpen={setOpenCropper}
                            setSrc={setSelectedCrop}
                        />
                    </div>
                </div>
            </div>

            <div className="mt-4 flex justify-end p-4 sm:px-6">
                <button
                    type="submit"
                    className="ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
                >
                    Save
                </button>
            </div>
        </form>
    );
}
