import DiscordIcon from "@/assets/icons/logos/DiscordIcon";
import EmailSettings from "@/features/settings/components/EmailSettings";
import InterfaceSettings from "@/features/settings/components/InterfaceSettings";
import NotificationSettings from "@/features/settings/components/NotificationSettings";
import PasswordSettings from "@/features/settings/components/PasswordSettings";
import ProfileSettings from "@/features/settings/components/ProfileSettings";
import { handleLogout } from "@/helpers/handleLogout";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { AtSign, Bell, CircleUser, Key, LayoutGrid, Settings as SettingsIcon, Users } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

export default function Settings() {
    const { data: currentUser } = useFetchCurrentUser();
    const [selectedPage, setSelectedPage] = useState("Profile");

    const confirmLogout = () => {
        window.confirm("Are you sure you want to logout?") && handleLogout();
    };

    const currentTab = (tabname) => {
        if (selectedPage === tabname) {
            return true;
        } else {
            return false;
        }
    };

    const subNavigation = [
        { name: "Profile", current: currentTab("Profile"), icon: CircleUser },
        { name: "Email", current: currentTab("Email"), icon: AtSign },
        { name: "Password", current: currentTab("Password"), icon: Key },
        {
            name: "Interface",
            current: currentTab("Interface"),
            icon: LayoutGrid,
        },
        {
            name: "Notifications",
            current: currentTab("Notifications"),
            icon: Bell,
        },
    ];

    const renderPage = () => {
        switch (selectedPage) {
            case "Profile":
                return <ProfileSettings currentUser={currentUser} />;
            case "Email":
                return <EmailSettings currentUser={currentUser} />;
            case "Password":
                return <PasswordSettings currentUser={currentUser} />;
            case "Interface":
                return <InterfaceSettings currentUser={currentUser} />;
            case "Notifications":
                return <NotificationSettings currentUser={currentUser} />;
            default:
                return <ProfileSettings currentUser={currentUser} />;
        }
    };

    return (
        <div className="overflow-hidden bg-white text-shadow shadow-sm md:mx-auto md:max-w-6xl md:rounded-lg dark:bg-gray-800 dark:md:border dark:md:border-gray-600">
            <div className="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-x lg:divide-y-0 dark:divide-gray-600">
                <aside className="pt-1 md:py-6 lg:col-span-3">
                    <nav className="space-y-1 ">
                        {subNavigation.map((item) => (
                            <button
                                key={item.name}
                                aria-current={item.current ? "page" : undefined}
                                className={cn(
                                    item.current
                                        ? "border-teal-500 bg-teal-50 text-teal-700 dark:bg-gray-900 dark:text-teal-300 dark:hover:text-teal-400"
                                        : "border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-900",
                                    "group flex w-full cursor-pointer items-center border-l-4 px-3 py-2 font-medium text-sm text-stroke-sm"
                                )}
                                onClick={() => setSelectedPage(item.name)}
                            >
                                <item.icon
                                    aria-hidden="true"
                                    className={cn(
                                        item.current
                                            ? "text-teal-500 group-hover:text-teal-500"
                                            : "text-gray-200 group-hover:text-gray-300",
                                        "-ml-1 mr-3 size-6 shrink-0"
                                    )}
                                />
                                <span className="truncate px-0.5">{item.name}</span>
                            </button>
                        ))}
                        {/* PREMIUM */}
                        {/* <button
              className={
                "border-transparent text-gray-300 group border-l-4 px-3 py-2 flex items-center text-sm font-medium w-full cursor-default"
              }
            >
              <CreditCardIcon
                className={"text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6"}
                aria-hidden="true"
              />
              <span className="truncate text-gray-500">Premium</span>
            </button> */}
                        {/* SIGN OUT */}
                        <button
                            className={
                                "group flex w-full cursor-pointer items-center border-transparent border-l-4 px-3 py-2 font-medium text-gray-900 text-sm text-stroke-sm hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-900"
                            }
                            onClick={confirmLogout}
                        >
                            <SettingsIcon
                                className={"-ml-1 mr-3 size-6 shrink-0 text-gray-200 group-hover:text-gray-300"}
                                aria-hidden="true"
                            />
                            <span className="truncate">Sign Out</span>
                        </button>
                    </nav>
                </aside>

                <div className="lg:col-span-9">
                    {/* Account Actions */}
                    <div className="px-4 py-6 sm:p-6 lg:pb-8">
                        <div className="mb-6">
                            <h2 className="font-medium text-gray-900 text-lg leading-6 dark:text-gray-100">
                                Account Actions
                            </h2>
                            <p className="mt-1 text-gray-500 text-sm dark:text-gray-400">
                                Quick actions for your account setup and referrals.
                            </p>
                        </div>

                        <div className="flex flex-col gap-4 sm:flex-row">
                            {!currentUser?.discordID && (
                                <Link to="/discord">
                                    <button
                                        type="button"
                                        className="flex flex-row font-display items-center gap-1.5 rounded-md border border-indigo-700 bg-indigo-600 px-4 py-2 font-medium text-gray-200 text-sm text-stroke-sm shadow-xs hover:bg-indigo-600/75 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    >
                                        <DiscordIcon className="mt-0.5 size-4" />
                                        Link with Discord
                                    </button>
                                </Link>
                            )}

                            {currentUser?.level > 4 && (
                                <Link to="/refer">
                                    <button
                                        type="button"
                                        className="flex flex-row font-display items-center gap-1.5 rounded-md border border-blue-700 bg-blue-600 px-4 py-2 font-medium text-gray-200 text-sm text-stroke-sm shadow-xs hover:bg-blue-600/75 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                    >
                                        <Users className="size-4" />
                                        Refer a Friend
                                    </button>
                                </Link>
                            )}
                        </div>

                        <hr className="mt-6 border-gray-200 dark:border-gray-600" />
                    </div>

                    {renderPage()}
                </div>
            </div>
        </div>
    );
}
